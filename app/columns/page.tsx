"use client";

import SectionCard from "@/app/components/SectionCard";
import ColumnComparisonTable from "@/app/components/ColumnComparisonTable";
import { useColumnComparison } from "@/app/hooks/useColumnComparison";
import { useAppContext } from "@/app/contexts/AppContext";
import { Button } from "@/components/ui/button";
import { RefreshCw } from "lucide-react";

export default function ColumnsPage() {
  const { authToken } = useAppContext();

  const {
    columns,
    pageTypes,
    columnsLoading,
    visualLoading,
    columnsError,
    visualError,
    fetchColumns,
    fetchVisualAds,
  } = useColumnComparison();

  const handleRefreshData = () => {
    fetchVisualAds();
    if (authToken) {
      fetchColumns();
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Column Comparison Tool</h1>
        <p className="text-muted-foreground">
          Compare page types from visual.json with column slugs from the backend
          API
        </p>
      </div>

      {/* Data Actions */}
      <SectionCard
        number={1}
        title="Data Actions"
        description="Load visual.json data and fetch columns from backend."
      >
        <div className="flex gap-4">
          <Button
            onClick={handleRefreshData}
            disabled={visualLoading || columnsLoading}
            className="flex items-center gap-2"
          >
            <RefreshCw
              className={`h-4 w-4 ${
                visualLoading || columnsLoading ? "animate-spin" : ""
              }`}
            />
            Refresh Data
          </Button>
          <Button
            onClick={fetchColumns}
            disabled={!authToken || columnsLoading}
            variant="outline"
          >
            {columnsLoading ? "Loading..." : "Fetch Columns"}
          </Button>
        </div>
      </SectionCard>

      {/* Comparison Table */}
      <SectionCard
        number={2}
        title="Comparison Table"
        description="Side-by-side comparison of page types and column slugs."
      >
        <ColumnComparisonTable
          pageTypes={pageTypes}
          columns={columns}
          columnsLoading={columnsLoading}
          visualLoading={visualLoading}
          columnsError={columnsError}
          visualError={visualError}
        />
      </SectionCard>
    </div>
  );
}
