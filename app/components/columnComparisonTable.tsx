"use client";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ColumnData } from "../definitions/app.types";
import { normalizeColumnName } from "../utils/column-normalization.util";

interface ColumnComparisonTableProps {
  pageTypes: string[];
  columns: ColumnData[];
  columnsLoading: boolean;
  visualLoading: boolean;
  columnsError: string | null;
  visualError: string | null;
}

export default function ColumnComparisonTable({
  pageTypes,
  columns,
  columnsLoading,
  visualLoading,
  columnsError,
  visualError,
}: ColumnComparisonTableProps) {
  const isLoading = columnsLoading || visualLoading;
  const hasError = columnsError || visualError;

  if (hasError) {
    return (
      <Alert className="mb-4">
        <AlertDescription>
          {columnsError && <div>Columns Error: {columnsError}</div>}
          {visualError && <div>Visual Data Error: {visualError}</div>}
        </AlertDescription>
      </Alert>
    );
  }

  if (isLoading) {
    return (
      <Alert className="mb-4">
        <AlertDescription>
          Loading data...
          {visualLoading && " (Loading visual.json)"}
          {columnsLoading && " (Loading columns from backend)"}
        </AlertDescription>
      </Alert>
    );
  }

  // Filter to only active columns and add "column_" prefix to slugs
  // Also normalize slugs to handle underscore/hyphen differences (e.g., "column_sport-futball" vs "column_sport_futball")
  const activeColumns = columns
    .filter((column) => column.isActive === 1)
    .map((column) => ({
      ...column,
      prefixedSlug: `column_${column.slug}`,
      normalizedSlug: normalizeColumnName(`column_${column.slug}`),
    }));

  // Create matching logic
  const createMatches = () => {
    const matches: Array<{
      pageType?: string;
      column?: (typeof activeColumns)[0];
      isMatch: boolean;
    }> = [];

    // Add matched pairs first
    const matchedPageTypes = new Set<string>();
    const matchedColumns = new Set<string>();

    pageTypes.forEach((pageType) => {
      const normalizedPageType = normalizeColumnName(pageType);
      const matchingColumn = activeColumns.find(
        (col) => col.normalizedSlug === normalizedPageType
      );
      if (matchingColumn) {
        matches.push({
          pageType,
          column: matchingColumn,
          isMatch: true,
        });
        matchedPageTypes.add(pageType);
        matchedColumns.add(matchingColumn.prefixedSlug);
      }
    });

    // Add unmatched page types
    pageTypes.forEach((pageType) => {
      if (!matchedPageTypes.has(pageType)) {
        matches.push({
          pageType,
          column: undefined,
          isMatch: false,
        });
      }
    });

    // Add unmatched columns
    activeColumns.forEach((column) => {
      if (!matchedColumns.has(column.prefixedSlug)) {
        matches.push({
          pageType: undefined,
          column,
          isMatch: false,
        });
      }
    });

    return matches;
  };

  const matches = createMatches();

  // Get the maximum number of rows needed
  const maxRows = matches.length;

  if (maxRows === 0) {
    return (
      <Alert className="mb-4">
        <AlertDescription>
          No data available. Please select a portal and ensure visual.json
          exists, then fetch columns from backend.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-4">
      {/* Summary */}
      <div className="grid grid-cols-3 gap-4">
        <Alert>
          <AlertDescription>
            <strong>Page Types:</strong> {pageTypes.length} unique types found
            in visual.json
          </AlertDescription>
        </Alert>
        <Alert>
          <AlertDescription>
            <strong>Active Columns:</strong> {activeColumns.length} active
            columns found in backend
          </AlertDescription>
        </Alert>
        <Alert className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950">
          <AlertDescription>
            <strong>Matches:</strong> {matches.filter((m) => m.isMatch).length}{" "}
            exact matches found
          </AlertDescription>
        </Alert>
      </div>

      {/* Comparison Table */}
      <div className="overflow-x-auto w-full">
        <Table className="w-full table-fixed">
          <TableHeader>
            <TableRow>
              <TableHead className="w-[5%]">#</TableHead>
              <TableHead className="w-[47.5%]">
                Page Type (from visual.json)
              </TableHead>
              <TableHead className="w-[47.5%]">
                Column Slug (from backend)
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {matches.map((match, index) => {
              return (
                <TableRow
                  key={index}
                  className={
                    match.isMatch
                      ? "bg-green-50 dark:bg-green-950/30 border-green-200 dark:border-green-800"
                      : ""
                  }
                >
                  <TableCell className="text-center">{index + 1}</TableCell>
                  <TableCell className="truncate" title={match.pageType || ""}>
                    {match.pageType ? (
                      <Badge
                        variant={match.isMatch ? "default" : "secondary"}
                        className={`max-w-full ${
                          match.isMatch ? "bg-green-600 hover:bg-green-700" : ""
                        }`}
                      >
                        {match.pageType}
                      </Badge>
                    ) : (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </TableCell>
                  <TableCell
                    className="truncate"
                    title={match.column?.prefixedSlug || ""}
                  >
                    {match.column ? (
                      <div className="space-y-1">
                        <Badge
                          variant={match.isMatch ? "default" : "outline"}
                          className={`max-w-full ${
                            match.isMatch
                              ? "bg-green-600 hover:bg-green-700"
                              : ""
                          }`}
                        >
                          {match.column.prefixedSlug}
                        </Badge>
                        <div
                          className="text-xs text-muted-foreground truncate"
                          title={match.column.title}
                        >
                          {match.column.title}
                        </div>
                      </div>
                    ) : (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
