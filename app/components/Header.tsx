"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { ThemeToggle } from "./ThemeToggle";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useAppContext } from "@/app/contexts/AppContext";
import { Selector } from "./Selector";
import { environments, portals } from "@/app/constants/app.consts";
import NavbarLoginForm from "./NavbarLoginForm";

export function Header() {
  const pathname = usePathname();
  const {
    environment,
    selectedPortal,
    authToken,
    onSelectEnvironment,
    onSelectPortal,
    setAuthToken,
  } = useAppContext();

  return (
    <header className="sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="flex justify-between p-4">
        {/* First row: Title and Navigation */}
        <div className="flex h-14 items-center mb-4">
          <div className="mr-4 flex">
            <Link
              href="/"
              className="text-xl font-bold hover:text-primary transition-colors"
            >
              KESMA Advertisement Automatizer
            </Link>
          </div>
          <nav className="flex items-center space-x-2 mx-6">
            <Button
              variant={pathname === "/" ? "default" : "ghost"}
              size="sm"
              asChild
            >
              <Link href="/">Advertisements</Link>
            </Button>
            <Button
              variant={pathname === "/columns" ? "default" : "ghost"}
              size="sm"
              asChild
            >
              <Link href="/columns">Columns</Link>
            </Button>
          </nav>
        </div>

        {/* Second row: Environment, Portal, and Login */}
        <div className="flex items-center gap-6 pb-4">
          <div className="flex items-center gap-4">
            <div className="min-w-[200px]">
              <Selector
                onSelect={onSelectEnvironment}
                selectedValue={environment}
                options={environments}
                label="Environment"
              />
            </div>
            <div className="min-w-[200px]">
              <Selector
                onSelect={onSelectPortal}
                selectedValue={selectedPortal.portal}
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                options={portals?.[environment] as any}
                label="Portal"
              />
            </div>
          </div>
          <div className="align-centerflex-1">
            <NavbarLoginForm
              setAuthToken={setAuthToken}
              authToken={authToken}
              selectedPortal={selectedPortal}
              environment={environment}
            />
          </div>

          <div className="flex flex-1 items-center justify-end">
            <ThemeToggle />
          </div>
        </div>
      </div>
    </header>
  );
}
