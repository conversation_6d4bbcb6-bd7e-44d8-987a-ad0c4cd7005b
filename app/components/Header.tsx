"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { ThemeToggle } from "./ThemeToggle";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

export function Header() {
  const pathname = usePathname();

  return (
    <header className="sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="p-4 flex h-14 items-center">
        <div className="mr-4 flex">
          <Link href="/" className="text-xl font-bold hover:text-primary transition-colors">
            KESMA Advertisement Automatizer
          </Link>
        </div>
        <nav className="flex items-center space-x-2 mx-6">
          <Button
            variant={pathname === "/" ? "default" : "ghost"}
            size="sm"
            asChild
          >
            <Link href="/">Advertisements</Link>
          </Button>
          <Button
            variant={pathname === "/columns" ? "default" : "ghost"}
            size="sm"
            asChild
          >
            <Link href="/columns">Columns</Link>
          </Button>
        </nav>
        <div className="flex flex-1 items-center justify-end">
          <ThemeToggle />
        </div>
      </div>
    </header>
  );
}
