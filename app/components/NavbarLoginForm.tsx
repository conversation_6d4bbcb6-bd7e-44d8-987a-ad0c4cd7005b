"use client";

import { FormEvent, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { PortalOption } from "../definitions/app.types";

interface NavbarLoginFormProps {
  selectedPortal: PortalOption;
  setAuthToken: (token: string) => void;
  authToken: string;
  environment: string;
}

export default function NavbarLoginForm({
  selectedPortal,
  setAuthToken,
  authToken,
  environment,
}: NavbarLoginFormProps) {
  const [email, setEmail] = useState("<EMAIL>");
  const [password, setPassword] = useState("qwertz");
  const [tokenInput, setTokenInput] = useState("");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [useTokenInput, setUseTokenInput] = useState(false);

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      const response = await fetch("/api/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email,
          password,
          portal: selectedPortal,
          environment,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Login failed");
      }

      if (typeof window !== "undefined") {
        setAuthToken(data.token);
      }
    } catch (err) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      setError((err as any).message);
    } finally {
      setLoading(false);
    }
  };

  const handleSetToken = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!tokenInput.trim()) {
      setError("Token is required");
      return;
    }

    setError("");
    setAuthToken(tokenInput.trim());
  };

  if (authToken) {
    return (
      <div className="flex items-center gap-2 text-sm">
        <span className="text-green-600 dark:text-green-400 font-medium">
          ✓ Authenticated
        </span>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setAuthToken("")}
        >
          Logout
        </Button>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-2">
      {/* Toggle buttons */}
      <div className="inline-flex rounded-md shadow-sm" role="group">
        <button
          type="button"
          onClick={() => setUseTokenInput(false)}
          className={cn(
            "px-2 py-1 text-xs font-medium border rounded-l-md",
            !useTokenInput
              ? "bg-primary text-primary-foreground border-primary"
              : "bg-background text-muted-foreground border-border hover:bg-muted/50"
          )}
        >
          Login
        </button>
        <button
          type="button"
          onClick={() => setUseTokenInput(true)}
          className={cn(
            "px-2 py-1 text-xs font-medium border rounded-r-md",
            useTokenInput
              ? "bg-primary text-primary-foreground border-primary"
              : "bg-background text-muted-foreground border-border hover:bg-muted/50"
          )}
        >
          Token
        </button>
      </div>

      {useTokenInput ? (
        <form onSubmit={handleSetToken} className="flex items-center gap-2">
          <Input
            type="text"
            value={tokenInput}
            onChange={(e) => setTokenInput(e.target.value)}
            placeholder="Enter token"
            className="w-48 h-8 text-sm"
            required
          />
          <Button type="submit" size="sm" className="h-8">
            Set
          </Button>
        </form>
      ) : (
        <form onSubmit={handleSubmit} className="flex items-center gap-2">
          <Input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="Email"
            className="w-32 h-8 text-sm"
            required
          />
          <Input
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            placeholder="Password"
            className="w-24 h-8 text-sm"
            required
          />
          <Button
            type="submit"
            disabled={loading}
            size="sm"
            className={cn("h-8", loading && "opacity-70")}
          >
            {loading ? "..." : "Login"}
          </Button>
        </form>
      )}

      {error && (
        <div className="text-xs text-destructive max-w-32 truncate" title={error}>
          {error}
        </div>
      )}
    </div>
  );
}
