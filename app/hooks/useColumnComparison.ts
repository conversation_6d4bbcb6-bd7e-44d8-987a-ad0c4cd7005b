import { useState, useEffect, useCallback } from "react";
import { ColumnData } from "../definitions/app.types";
import { BackendAdvertisement } from "../definitions/adverts.definitions";
import { useAppContext } from "../contexts/AppContext";

export function useColumnComparison() {
  const { selectedPortal, authToken, environment } = useAppContext();
  const [columns, setColumns] = useState<ColumnData[]>([]);
  const [visualAds, setVisualAds] = useState<BackendAdvertisement[]>([]);
  const [pageTypes, setPageTypes] = useState<string[]>([]);
  const [columnsLoading, setColumnsLoading] = useState<boolean>(false);
  const [visualLoading, setVisualLoading] = useState<boolean>(false);
  const [columnsError, setColumnsError] = useState<string | null>(null);
  const [visualError, setVisualError] = useState<string | null>(null);

  // Fetch columns from backend
  const fetchColumns = useCallback(async () => {
    if (!authToken) {
      return;
    }

    setColumnsLoading(true);
    setColumnsError(null);

    try {
      const res = await fetch("/api/columns/get", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          token: authToken,
          portal: selectedPortal.portal,
          url: selectedPortal.api,
          environment,
        }),
      });

      const result = await res.json();
      if (res.ok && result.success) {
        setColumns(result.columns);
      } else {
        setColumnsError(result.error || "Failed to fetch columns");
      }
    } catch (err) {
      console.error("Error fetching columns:", err);
      setColumnsError("Failed to fetch columns");
    } finally {
      setColumnsLoading(false);
    }
  }, [authToken, selectedPortal, environment]);

  // Fetch visual.json data
  const fetchVisualAds = useCallback(async () => {
    setVisualLoading(true);
    setVisualError(null);

    try {
      const res = await fetch("/api/get-local-ads", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          portal: selectedPortal.portal,
        }),
      });

      const result = await res.json();
      if (res.ok && result.exists) {
        setVisualAds(result.ads);

        // Extract unique pageTypes
        const uniquePageTypes = Array.from(
          new Set(result.ads.map((ad: BackendAdvertisement) => ad.pageType))
        ).sort() as string[];
        setPageTypes(uniquePageTypes);
      } else {
        setVisualError("No visual.json file found for this portal");
        setVisualAds([]);
        setPageTypes([]);
      }
    } catch (err) {
      console.error("Error fetching visual ads:", err);
      setVisualError("Failed to load visual.json");
      setVisualAds([]);
      setPageTypes([]);
    } finally {
      setVisualLoading(false);
    }
  }, [selectedPortal.portal]);

  // Fetch visual ads when portal changes
  useEffect(() => {
    fetchVisualAds();
  }, [fetchVisualAds]);

  // Reset columns when portal or auth changes
  useEffect(() => {
    setColumns([]);
    setColumnsError(null);
  }, [selectedPortal, authToken]);

  return {
    columns,
    pageTypes,
    visualAds,
    columnsLoading,
    visualLoading,
    columnsError,
    visualError,
    fetchColumns,
    fetchVisualAds,
  };
}
