export interface EnvironmentOption {
  label: string;
  value: string;
}

export interface PortalOption {
  label: string;
  api: string;
  portal: string;
  jsonIdentifier: string;
}

export type PortalsByEnvironments = {
  [key: string]: PortalOption[];
};

export interface ColumnData {
  id: string;
  title: string;
  lead: string | null;
  slug: string;
  isActive: number;
  isHidden: number;
  seoTitle: string | null;
  seoLead: string | null;
  emphasizeOnArticleCard: number;
  thumbnail: string | null;
  stat: number;
  rowActions: string[];
}
