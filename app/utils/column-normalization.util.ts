/**
 * Utility functions for normalizing column names and slugs to handle
 * inconsistent underscore/hyphen usage between different data sources
 */

/**
 * Normalize column names by converting hyphens to underscores for consistent matching
 * This handles cases like:
 * - "column_sport-futball" -> "column_sport_futball"
 * - "column_sport_futball" -> "column_sport_futball" (no change)
 * - "sport-news-main" -> "sport_news_main"
 *
 * @param name - The column name or slug to normalize
 * @returns The normalized name with hyphens converted to underscores
 */
export const normalizeColumnName = (name: string): string => {
  return name.replace(/-/g, "_");
};

/**
 * Check if two column names match after normalization
 * This is useful for comparing column names from different sources that might
 * use different separator conventions (hyphens vs underscores)
 *
 * @param name1 - First column name to compare
 * @param name2 - Second column name to compare
 * @returns True if the normalized names match, false otherwise
 */
export const columnNamesMatch = (name1: string, name2: string): boolean => {
  return normalizeColumnName(name1) === normalizeColumnName(name2);
};

/**
 * Find a matching column from an array based on normalized name comparison
 *
 * @param targetName - The name to search for
 * @param columns - Array of objects with a name property to search through
 * @param nameProperty - The property name to use for comparison (default: 'name')
 * @returns The matching column object or undefined if no match found
 */
export const findMatchingColumn = <T extends Record<string, any>>(
  targetName: string,
  columns: T[],
  nameProperty: keyof T = "name"
): T | undefined => {
  const normalizedTarget = normalizeColumnName(targetName);
  return columns.find(
    (column) =>
      normalizeColumnName(String(column[nameProperty])) === normalizedTarget
  );
};

/**
 * Create a normalized lookup map for fast column matching
 *
 * @param columns - Array of column objects
 * @param nameProperty - The property name to use as the key (default: 'name')
 * @returns Map with normalized names as keys and original objects as values
 */
export const createNormalizedColumnMap = <T extends Record<string, any>>(
  columns: T[],
  nameProperty: keyof T = "name"
): Map<string, T> => {
  const map = new Map<string, T>();
  columns.forEach((column) => {
    const normalizedName = normalizeColumnName(String(column[nameProperty]));
    map.set(normalizedName, column);
  });
  return map;
};
