"use client";

import { createContext, useContext, useState, ReactNode, useEffect } from "react";
import { environments, portals } from "@/app/constants/app.consts";
import { PortalOption } from "@/app/definitions/app.types";

interface AppContextType {
  environment: string;
  selectedPortal: PortalOption;
  authToken: string;
  setEnvironment: (env: string) => void;
  setSelectedPortal: (portal: PortalOption) => void;
  setAuthToken: (token: string) => void;
  onSelectEnvironment: (environment: string) => void;
  onSelectPortal: (portal: string) => void;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

export function AppProvider({ children }: { children: ReactNode }) {
  const [environment, setEnvironment] = useState(environments[0].value);
  const [selectedPortal, setSelectedPortal] = useState<PortalOption>(
    portals?.[environments[0].value]?.[0]
  );
  const [authToken, setAuthToken] = useState("");

  const onSelectEnvironment = (environment: string) => {
    setEnvironment(environment);
    setAuthToken("");
    // Reset to first portal of the new environment
    if (portals?.[environment]?.[0]) {
      setSelectedPortal(portals[environment][0]);
    }
  };

  const onSelectPortal = (portal: string) => {
    const selected = portals[environment].find(
      (portalItem) => portalItem.portal === portal
    );
    setAuthToken("");

    if (selected) setSelectedPortal(selected);
  };

  // Initialize portal when environment changes
  useEffect(() => {
    if (portals?.[environment]?.[0]) {
      setSelectedPortal(portals[environment][0]);
    }
  }, [environment]);

  const value: AppContextType = {
    environment,
    selectedPortal,
    authToken,
    setEnvironment,
    setSelectedPortal,
    setAuthToken,
    onSelectEnvironment,
    onSelectPortal,
  };

  return <AppContext.Provider value={value}>{children}</AppContext.Provider>;
}

export function useAppContext() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error("useAppContext must be used within an AppProvider");
  }
  return context;
}
