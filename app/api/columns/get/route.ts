import { getProtocolByEnv } from "@/app/utils/protocol-by-environment.util";
import { NextRequest } from "next/server";

export async function POST(req: NextRequest) {
  const { token, portal, url, environment } = await req.json();
  
  if (!token || !portal || !url) {
    return new Response(
      JSON.stringify({
        error: "Missing required parameters",
        success: false
      }),
      { status: 400 }
    );
  }

  try {
    const apiUrl = `${getProtocolByEnv(
      environment
    )}://${url}/api/hu/hu/content-group/columns?page_limit=0&offset_limit=0&rowCount_limit=1000`;

    const requestOptions = {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "X-Auth-Token": token,
        Portal: portal,
      },
    };

    const response = await fetch(apiUrl, requestOptions);

    if (!response.ok) {
      return new Response(
        JSON.stringify({
          error: `Error fetching columns: ${response.status} ${response.statusText}`,
          success: false
        }),
        { status: response.status }
      );
    }

    const json = await response.json();

    // Validate response format
    if (!json.data || !Array.isArray(json.data)) {
      return new Response(
        JSON.stringify({
          error: "Invalid response format from backend",
          success: false
        }),
        { status: 500 }
      );
    }

    return new Response(JSON.stringify({ success: true, columns: json.data }), {
      status: 200,
    });
  } catch (error) {
    return new Response(
      JSON.stringify({
        error: (error as Error)?.message || "Request failed",
        success: false
      }),
      {
        status: 500,
      }
    );
  }
}
